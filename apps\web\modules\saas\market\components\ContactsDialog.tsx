import React, { useState, useEffect } from "react";
import { Input } from "@ui/components/input";
import { Search, SquarePlus } from "lucide-react";
import {
  Card,
  CardContent,
} from "@ui/components/card";
import { Edit, X, Save } from "lucide-react";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import {
  useContacts,
  useCreateContact,
  useUpdateContact,
  useDeleteContact
} from "../hooks/useContacts";
import type { Contact } from "../lib/contacts_data";
import { useParams } from "next/navigation";



/**
 * 本地联系人状态类型（包含编辑状态和动画状态）
 */
interface LocalContact extends Contact {
  isEditing: boolean;
  isAnimating?: boolean; // 是否正在执行动画
  animationType?: 'slideDown' | 'slideUp'; // 动画类型
}

/**
 * 联系人对话框组件
 * @param cardCode 当前选中的卡片代码，用于触发每次切换卡片时的查询
 * @returns {JSX.Element}
 */
export function ContactsDialog({ cardCode }: { cardCode?: string }) {
  const { activeOrganization } = useActiveOrganization();
  const params = useParams();
  const organizationSlug = params.organizationSlug as string;



  // 搜索关键字状态
  const [search, setSearch] = useState("");
  // 错误提示状态
  const [errorMessage, setErrorMessage] = useState("");
  // 错误类型状态，用于控制不同的消失逻辑
  const [errorType, setErrorType] = useState<"" | "name_required" | "save_first">("");
  // 本地联系人状态（包含编辑状态）
  const [localContacts, setLocalContacts] = useState<LocalContact[]>([]);

  // 使用 organizationSlug 作为 organizationId（因为后端可能期望的是 slug 而不是 id）
  const organizationId = activeOrganization?.id || organizationSlug || "";

  // 获取联系人列表
  const { data: contactsData, isLoading, refetch } = useContacts(
    {
      organizationId,
      cardCode, // 传入卡片代码，每次切换卡片时触发新查询
      keyword: search,
      page: 1,
      limit: 100,
    },
    {
      enabled: !!organizationId,
    }
  );



  // 创建联系人 mutation
  const createContactMutation = useCreateContact({
    onSuccess: () => {
      refetch();
      setErrorMessage("");
      setErrorType("");
    },
    onError: (error) => {
      setErrorMessage(error.message || "创建失败");
      setErrorType("save_first");
    },
  });

  // 更新联系人 mutation
  const updateContactMutation = useUpdateContact({
    onSuccess: () => {
      refetch();
      setErrorMessage("");
      setErrorType("");
    },
    onError: (error) => {
      setErrorMessage(error.message || "更新失败");
      setErrorType("save_first");
    },
  });

  // 删除联系人 mutation
  const deleteContactMutation = useDeleteContact({
    onSuccess: () => {
      refetch();
      setErrorMessage("");
      setErrorType("");
    },
    onError: (error) => {
      setErrorMessage(error.message || "删除失败");
      setErrorType("save_first");
    },
  });

  // 同步服务器数据到本地状态
  useEffect(() => {
    if (contactsData?.contacts) {
      setLocalContacts(prev => {
        // 保留正在动画的临时联系人
        const animatingTempContacts = prev.filter(c =>
          c.contactId.startsWith('temp_') && c.isAnimating
        );

        // 合并服务器数据和动画中的临时联系人
        const serverContacts = contactsData.contacts.map(contact => ({
          ...contact,
          isEditing: false,
          isAnimating: false,
          animationType: undefined,
        }));

        return [...animatingTempContacts, ...serverContacts];
      });
    }
  }, [contactsData]);

  /**
   * 处理搜索输入变化
   */
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  /**
   * 处理新增联系人
   * 新增联系人自动进入编辑状态，插入到列表首位
   */
  const handleAddContact = () => {
    if (!organizationId) {
      setErrorMessage("请先选择组织");
      setErrorType("save_first");
      return;
    }

    // 检查是否有未保存的联系人
    const hasUnsavedContact = localContacts.some(contact => contact.isEditing);
    if (hasUnsavedContact) {
      setErrorMessage("请先保存");
      setErrorType("save_first");
      return;
    }

    // 清除之前的错误信息
    setErrorMessage("");
    setErrorType("");

    // 生成临时ID（负数表示新建）
    const tempId = `temp_${Date.now()}`;
    const newContact: LocalContact = {
      contactId: tempId,
      organizationId,
      name: "",
      phoneNumber: "",
      email: "",
      address: "",
      remarks: "",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: "",
      updatedBy: "",
      isEditing: true,
      isAnimating: true,
      animationType: 'slideDown',
    };

    setLocalContacts([newContact, ...localContacts]);

    // 动画完成后移除动画状态
    setTimeout(() => {
      setLocalContacts(prev => prev.map(c =>
        c.contactId === tempId
          ? { ...c, isAnimating: false, animationType: undefined }
          : c
      ));
    }, 300); // 300ms 动画时长
  };

  /**
   * 处理切换联系人编辑状态
   */
  const handleEdit = (contactId: string) => {
    // 检查是否有其他联系人正在编辑
    const hasOtherEditing = localContacts.some(contact => contact.isEditing && contact.contactId !== contactId);

    if (hasOtherEditing) {
      setErrorMessage("请先保存");
      setErrorType("save_first");
      return;
    }

    // 清除错误信息并进入编辑状态
    setErrorMessage("");
    setErrorType("");
    setLocalContacts(localContacts.map(c => c.contactId === contactId ? { ...c, isEditing: true } : c));
  };

  /**
   * 处理保存联系人编辑
   */
  const handleSave = async (contactId: string) => {
    if (!organizationId) {
      setErrorMessage("请先选择组织");
      setErrorType("save_first");
      return;
    }

    // 查找要保存的联系人
    const contactToSave = localContacts.find(c => c.contactId === contactId);

    // 验证姓名是否为空
    if (!contactToSave?.name || contactToSave.name.trim() === "") {
      setErrorMessage("请填写姓名");
      setErrorType("name_required");
      return;
    }

    try {
      // 判断是新建还是更新
      const isNewContact = contactId.startsWith('temp_');

      if (isNewContact) {
        // 创建新联系人
        await createContactMutation.mutateAsync({
          organizationId,
          name: contactToSave.name,
          phoneNumber: contactToSave.phoneNumber || undefined,
          email: contactToSave.email || undefined,
          address: contactToSave.address || undefined,
          remarks: contactToSave.remarks || undefined,
        });
      } else {
        // 更新现有联系人
        await updateContactMutation.mutateAsync({
          contactId: contactToSave.contactId,
          organizationId,
          name: contactToSave.name,
          phoneNumber: contactToSave.phoneNumber || undefined,
          email: contactToSave.email || undefined,
          address: contactToSave.address || undefined,
          remarks: contactToSave.remarks || undefined,
        });
      }

      // 保存成功后退出编辑状态
      setLocalContacts(localContacts.map(c =>
        c.contactId === contactId ? { ...c, isEditing: false } : c
      ));
    } catch (error) {
      // 错误处理已在 mutation 的 onError 中处理
    }
  };

  /**
   * 处理删除联系人
   */
  const handleDelete = async (contactId: string) => {
    if (!organizationId) {
      setErrorMessage("请先选择组织");
      setErrorType("save_first");
      return;
    }

    // 检查被删除的联系人是否正在编辑状态
    const contactToDelete = localContacts.find(c => c.contactId === contactId);
    const isEditingContact = contactToDelete?.isEditing;
    const isNewContact = contactId.startsWith('temp_');

    // 先添加向上折叠动画
    setLocalContacts(prev => prev.map(c =>
      c.contactId === contactId
        ? { ...c, isAnimating: true, animationType: 'slideUp' as const }
        : c
    ));

    // 等待动画完成后再执行删除逻辑
    setTimeout(async () => {
      try {
        if (!isNewContact) {
          // 如果是已存在的联系人，调用删除API
          await deleteContactMutation.mutateAsync({
            contactId,
            organizationId,
          });
        } else {
          // 如果是新建的联系人，直接从本地状态删除
          setLocalContacts(prev => prev.filter(c => c.contactId !== contactId));

          // 如果删除的是正在编辑的联系人，清除相关错误提示
          if (isEditingContact) {
            if (errorType === "save_first" || errorType === "name_required") {
              setErrorMessage("");
              setErrorType("");
            }
          }
        }
      } catch (error) {
        // 如果删除失败，恢复动画状态
        setLocalContacts(prev => prev.map(c =>
          c.contactId === contactId
            ? { ...c, isAnimating: false, animationType: undefined }
            : c
        ));
      }
    }, 300); // 300ms 动画时长
  };

  /**
   * 处理联系人字段变化
   */
  const handleFieldChange = (contactId: string, field: keyof LocalContact, value: string) => {
    // 如果是姓名字段且当前错误是"请填写姓名"，则清除错误
    if (field === "name" && errorType === "name_required") {
      setErrorMessage("");
      setErrorType("");
    }

    setLocalContacts(localContacts.map(c =>
      c.contactId === contactId ? { ...c, [field]: value } : c
    ));
  };

  // 模糊搜索过滤联系人（搜索在服务端进行，这里只做本地显示过滤）
  const filteredContacts = localContacts.filter(c => {
    const keyword = search.trim().toLowerCase();
    if (!keyword) {return true;}
    return (
      c.name.toLowerCase().includes(keyword) ||
      c.phoneNumber.toLowerCase().includes(keyword) ||
      c.email.toLowerCase().includes(keyword)
    );
  });

  // 加载状态显示
  if (isLoading && localContacts.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center gap-2 mb-6">
          <div className="relative flex-1 pl-1.5">
            <Input
              placeholder="输入联系人姓名、手机号和邮箱进行搜索"
              value={search}
              onChange={handleSearchChange}
              className="pl-10 h-12 text-base"
              disabled
            />
            <Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
          </div>
          <button
            type="button"
            className="flex items-center justify-center w-12 h-12 rounded-lg border border-input opacity-50 cursor-not-allowed"
            disabled
          >
            <SquarePlus className="w-7 h-7" />
          </button>
        </div>
        <div className="text-center text-muted-foreground py-12">
          加载中...
        </div>
      </div>
    );
  }

  return (
			<div className="w-full">
				{/* 顶部搜索栏和加号按钮 */}
				<div className="flex items-center gap-2 mb-6">
					{/* 搜索输入框 */}
					<div className="relative flex-1 pl-1.5">
						<Input
							placeholder="输入联系人姓名、手机号和邮箱进行搜索"
							value={search}
							onChange={handleSearchChange}
							className="pl-10 h-12 text-base"
						/>
						{/* 搜索图标 */}
						<Search className="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-muted-foreground" />
					</div>
					{/* 加号按钮 */}
					<button
						type="button"
						className="flex items-center justify-center w-12 h-12 rounded-lg border border-input hover:bg-muted transition-colors cursor-pointer"
						onClick={handleAddContact}
						aria-label="新增联系人"
						title="新增联系人"
					>
						<SquarePlus className="w-7 h-7" />
					</button>
				</div>

				{/* 错误提示 */}
				{errorMessage && (
					<div className="mb-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
						<p className="text-red-600 dark:text-red-400 text-sm text-center">
							{errorMessage}
						</p>
					</div>
				)}

				{/* 联系人卡片列表 */}
				<div className="flex flex-col">
					{filteredContacts.map((contact) => (
						<Card
							key={contact.contactId}
							className={`relative border w-full overflow-hidden p-6 transition-all duration-300 ease-out ${
								contact.isAnimating && contact.animationType === 'slideDown'
									? 'animate-in slide-in-from-top-2 fade-in-0 mb-0'
									: contact.isAnimating && contact.animationType === 'slideUp'
									? 'animate-out slide-out-to-top-2 fade-out-0 mb-0 max-h-0 opacity-0'
									: 'mb-6'
							}`}
						>
							{/* 右上角编辑和删除按钮 */}
							<div className="absolute right-4 top-4 flex items-center gap-2 z-10">
								{contact.isEditing ? (
									<button
										type="button"
										className="p-1 rounded hover:bg-muted cursor-pointer"
										onClick={() => handleSave(contact.contactId)}
										aria-label="保存"
										title="保存"
									>
										<Save className="w-5 h-5" />
									</button>
								) : (
									<button
										type="button"
										className="p-1 rounded hover:bg-muted cursor-pointer"
										onClick={() => handleEdit(contact.contactId)}
										aria-label="编辑"
										title="编辑"
									>
										<Edit className="w-5 h-5" />
									</button>
								)}
								<button
									type="button"
									className="p-1 rounded hover:bg-muted cursor-pointer"
									onClick={() => handleDelete(contact.contactId)}
									aria-label="删除"
									title="删除"
								>
									<X className="w-5 h-5" />
								</button>
							</div>
							{/* 卡片内容区 */}
							<CardContent className="pt-2">
								{/* 姓名 */}
								<div className="mb-2 flex items-center font-medium text-lg break-words">
									{contact.isEditing ? (
										<>
											<span className="font-normal mr-2">
												姓名：
											</span>
											<Input
												value={contact.name}
												onChange={(e) =>
													handleFieldChange(
														contact.contactId,
														"name",
														e.target.value,
													)
												}
												placeholder="请输入姓名"
												className="text-base w-64"
											/>
										</>
									) : (
										<span className="break-words">
											{contact.name || (
												<span className="text-muted-foreground">
													未填写姓名
												</span>
											)}
										</span>
									)}
								</div>
								{/* 电话和邮箱 */}
								<div className="flex flex-col md:flex-row gap-y-2 gap-x-8 mb-2">
									<div className="flex-1 min-w-0">
										<span className="font-normal">
											电话号码：
										</span>
										{contact.isEditing ? (
											<Input
												value={contact.phoneNumber}
												onChange={(e) =>
													handleFieldChange(
														contact.contactId,
														"phoneNumber",
														e.target.value,
													)
												}
												placeholder="请输入电话号码"
												className="w-full"
											/>
										) : (
											<span className="break-words">
												{contact.phoneNumber || (
													<span className="text-muted-foreground">
														-
													</span>
												)}
											</span>
										)}
									</div>
									<div className="flex-1 min-w-0">
										<span className="font-normal">
											邮箱：
										</span>
										{contact.isEditing ? (
											<Input
												value={contact.email}
												onChange={(e) =>
													handleFieldChange(
														contact.contactId,
														"email",
														e.target.value,
													)
												}
												placeholder="请输入邮箱"
												className="w-full"
											/>
										) : (
											<span className="break-words">
												{contact.email || (
													<span className="text-muted-foreground">
														-
													</span>
												)}
											</span>
										)}
									</div>
								</div>
								{/* 地址 */}
								<div className="mb-2">
									<span className="font-normal">地址：</span>
									{contact.isEditing ? (
										<Input
											value={contact.address}
											onChange={(e) =>
												handleFieldChange(
													contact.contactId,
													"address",
													e.target.value,
												)
											}
											placeholder="请输入地址"
											className="w-full"
										/>
									) : (
										<span className="break-words">
											{contact.address || (
												<span className="text-muted-foreground">
													-
												</span>
											)}
										</span>
									)}
								</div>
								{/* 备注 */}
								<div>
									<span className="font-normal">备注 </span>
									{contact.isEditing ? (
										<Input
											value={contact.remarks}
											onChange={(e) =>
												handleFieldChange(
													contact.contactId,
													"remarks",
													e.target.value,
												)
											}
											placeholder="请输入备注"
											className="w-full"
										/>
									) : (
										<span className="break-words">
											{contact.remarks || (
												<span className="text-muted-foreground">
													{" "}
												</span>
											)}
										</span>
									)}
								</div>
							</CardContent>
						</Card>
				))}
				{filteredContacts.length === 0 && (
					<div className="text-center text-muted-foreground py-12">
						未找到匹配的联系人
					</div>
				)}
			</div>
		</div>
	);
}
